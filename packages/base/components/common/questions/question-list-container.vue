<script setup lang="ts">
import QsProvide from './qs-provide'
import QuestionItemContainer from './question-item-container.vue'

defineOptions({
  name: 'QuestionListContainer',
})

const questionList = defineModel<Question.ProcessedQuestionData[]>('questionList', {
  default: () => [],
})
</script>

<template>
  <QsProvide>
    <template v-for="item in questionList" :key="item.id">
      <QuestionItemContainer v-model="item" :item-info="item" />
    </template>
  </QsProvide>
</template>
